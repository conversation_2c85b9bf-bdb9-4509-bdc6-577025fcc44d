addpath(genpath('D:\code\PlatEMO-PlatEMO_v2.8.0\PlatEMO\Algorithms\REMO'))


clc
clear all
disp('with privacy protection')
Decs_temp = [];
runs = 20;

disp('W3C2O2')
rng('shuffle');
rng(randperm(2^32-1,1),'twister');

LL = 20;
for ll = 1:LL
    clearvars -except data ll;
    runs = 20;
    InternalRuns = 1;
    wmax = 100;
%     2:LCB  1:UCB  0:EI
    UCB_Flag= 2; 
    N_notR_ini = 100;

    ifu1 =  1;
    ifu2 =  1;
    %     IFU_ = [ifu1 ifu2];
    IFU_ = [1:18];

    client_num = length(IFU_);
    D = 10;

    p = 0
%     3 6 9 12 18
    cl_num = 6;
    flag_transfer = 1


    N = 21;
    MAXFE = 89;

    N + MAXFE

    for ifu = IFU_
        [name_{ifu},bu_{ifu},bd_{ifu},vmax_{ifu},vmin_{ifu},M_{ifu},opt_{ifu}] = ChooseProblem(ifu,D);
        A1Dec_INI_SUM{ifu} =lhsclassic(N,D).*(ones(N,1).*(bu_{ifu}-bd_{ifu}))+ones(N,1).*bd_{ifu};
        shuffle_id = randperm(N,N);
        temp= A1Dec_INI_SUM{ifu};
        A1Dec_INI_SUM{ifu} = temp(shuffle_id,:);
        if ~isempty(M_{ifu})
            A1Obj_INI_SUM{ifu} = feval(name_{ifu},A1Dec_INI_SUM{ifu},M_{ifu},opt_{ifu});
        else
            A1Obj_INI_SUM{ifu} = feval(name_{ifu},A1Dec_INI_SUM{ifu});
        end
        %     disp(min(A1Obj_INI_SUM1))
    end



    %     A1Dec_SUM = [A1Dec_INI_SUM1;A1Dec_INI_SUM2];
    %     A1Obj_SUM = [A1Obj_INI_SUM1;A1Obj_INI_SUM2];
    %     min(A1Obj)

    Decs_temp = [];
    PopNewObj = {};
    PopNewDec = {};
    fail_count = {};


    netsum = [];
    maxround = 0;
    for ff = 1:client_num
        THETA{ff} = 5.*ones(1,D);
        Model{ff} = cell(1,1);
    end

    while maxround < MAXFE
        %         str = '-------------------------';
        %         disp(str)
        %         disp(size(PopNewObj{1},1))

        %         new_infill_sum = size(A1Dec_SUM,1) - size(A1Dec_INI_SUM,1);
        Decs_sum = [];



        PopDecTest = rand(N_notR_ini,D).*(ones(N_notR_ini,1));

        for ff = 1:client_num

            Num_each = N;

            bu = bu_{ff};
            bd = bd_{ff};


            %             ff
            if isempty(PopNewObj)
                A1Obj_{ff} = A1Obj_INI_SUM{ff};
                A1Dec_{ff} = A1Dec_INI_SUM{ff};
            else
                A1Obj_{ff} = [A1Obj_INI_SUM{ff};PopNewObj{ff}];
                A1Dec_{ff} = [A1Dec_INI_SUM{ff};PopNewDec{ff}];
            end

            % GP FITTING



            A1Obj = A1Obj_{ff};
            A1Dec = A1Dec_{ff} ;

            % The parameter 'regpoly1' refers to one-order polynomial
            % function, and 'regpoly0' refers to constant function. The
            % former function has better fitting performance but lower
            % efficiency than the latter one
            A1Obj_norm  = (A1Obj - min(A1Obj) )./(max(A1Obj)-min(A1Obj));
            [C,ia,ic] = unique(A1Dec,'rows','stable');
            A1Dec = A1Dec(ia,:);
            A1Obj_norm = A1Obj_norm(ia,:);
            A1Obj = A1Obj(ia,:);


            dmodel     = dacefit(A1Dec,A1Obj_norm,'regpoly0','corrgauss',THETA{ff},1e-5.*ones(1,D),100.*ones(1,D));
            Model{ff}   = dmodel;
            THETA{ff} = dmodel.theta;

            %     lhsdesign
            PopDec = PopDecTest.*(bu-bd)+ones(N_notR_ini,1).*bd;

            PopObj = zeros(N_notR_ini,1);
            MSE    = zeros(N_notR_ini,1);

            for i = 1: N_notR_ini
                [PopObj(i,:),~,MSE(i,:)] = predictor(PopDec(i,:),Model{ff} );
            end

            % ANN classification

            Input  = A1Dec;
            objs = A1Obj_norm;
            Output = objs(:,1);
            objs = AF(PopObj,MSE,A1Obj_norm,UCB_Flag);


            id = 1;

            for i = 1:size(Input,1)
                for j = i+1: size(Input,1)
                    XXs(id,:) = [Input(i,:) Input(j,:)];
                    if Output(i) - Output(j) > 0
                        YYs(id,:) = 1;
                    elseif Output(i) - Output(j) < 0
                        YYs(id,:) = -1;
                    else
                        YYs(id,:) = 0;
                    end
                    id = id + 1;
                end

            end

            ix = randperm(id-1,id-1);
            XXs = XXs(ix,:);
            YYs = YYs(ix,:);


            aa = round(rand(size(YYs(1:round(p*(id-1)),:),1),1));
            aa(find(aa==0),:) = -1;
            YYs(1:round(p*(id-1)),:) = YYs(1:round(p*(id-1)),:) .* aa;

            ix = randperm(id-1,id-1);
            XXs = XXs(ix,:);
            YYs = YYs(ix,:);

            Input  = PopDec;
            Output = objs(1:size(PopDec,1),1);


            Input_ = [A1Dec;PopDec];
            Output_ = [Output;objs];


            Output1 = objs(size(PopDec,1)+1:end,1);
            id_1 = id-1;


            id = 1;
            for i = 1:size(Input,1)
                for j = i+1: size(Input,1)
                    XXs_(id,:) = [Input(i,:) Input(j,:)];
                    if Output(i) - Output(j) > 0
                        YYs_(id,:) = 1;
                    elseif Output(i) - Output(j) < 0
                        YYs_(id,:) = -1;
                    else
                        YYs_(id,:) = 0;
                    end
                    id = id + 1;
                end
            end



            ix = randperm(id-1,id-1);
            XXs_ = XXs_(ix,:);
            YYs_ = YYs_(ix,:);


            XXs = [XXs;XXs_];
            YYs = [YYs;YYs_];


            ix = randperm(size(XXs,1),size(XXs,1));
            XXs = XXs(ix,:);
            YYs = YYs(ix,:);

            [TrainIn,TrainOut,TestIn,TestOut] = DataProcess(XXs,YYs);

            xDim = size(TrainIn,2);
            % Train relation model


            TrainIn_nor = (TrainIn - bd) / (bu - bd);
            TrainOut_onehot = onehotconv(TrainOut,1);
            TrainOut_onehot(:,2) =[];

            %
            if isempty(PopNewObj)
                net = patternnet([ceil(xDim*1.5),xDim*1,ceil(xDim/2)]);
                net.trainParam.showWindow =0;
            else

                if flag_transfer == 1 && 1==1
                    % W1 aggregate all weights
                    %                     net  = net_agg;

                    % W2
                    % new added  USE its own weights
                    %                                         net  = netsum{ff};

                    % W3 new added2  aggregate similar weights
                    %                     CID = [ff,associate(ff,1:kk)];
                    %                     CID = CID';

                    CID = find(associate==associate(ff));
                    net = model_agg(netsum,CID');

                else
                    %                     net = patternnet([ceil(xDim*1.5),xDim*1,ceil(xDim/2)]);
                    %                     net.trainParam.showWindow =0;
                    net  = netsum{ff};
                end
                %                 net = patternnet([ceil(xDim*1.5),xDim*1,ceil(xDim/2)]);
                %                 net.trainParam.showWindow =0;
            end

            net = train(net,TrainIn_nor',TrainOut_onehot');

            netsum{ff} = net;


            TestIn_nor = (TestIn - bd) / (bu - bd);
            TestPre = onehotconv(net(TestIn_nor')',2);
            p_err = sum(TestPre ~= TestOut)/size(TestPre,1);
        end




        %         clustering the model weights
        weightvector = [];
        for ff = 1:client_num
            weightvector(ff,:) = ConvertModelParaToVector(netsum,ff);
        end

        %    C1     similary measurement method
        %         Angle = acos(1-pdist2(weightvector,weightvector,'cosine'));
        %         Angle(logical(eye(length(Angle)))) = inf;
        %         [angleSol,associate] = sort(Angle,2,'ascend');
        %         kk = 3;

        %    C2     similary measurement method2 C2
        [associate,~]=kmeans(weightvector,cl_num);

        id = 1;
        [~,minID] = min(A1Obj,[],1);

        popsize = 100;
        phi = 0.1;

        half_train_flag = 1;


        for ff = 1:client_num

            flag_agg = 0;
            CID = [];

            if length(PopNewDec) < client_num
                fail_count{ff} = 0;
            else
                objs = PopNewObj{ff};
                if (size(objs,1)>=2) && (min(objs(1:end,:)) == min(objs(1:end-1,:)))
                    fail_count{ff} = fail_count{ff}  + 1;
                else
                    fail_count{ff} = 0;
                end

            end

            if flag_transfer == 1
                %                 || fail_count{ff} > 2
                if rand < 0.5
                    %   O1  similary measurement method1
                    %   CID = [ff,associate(ff,1:kk)];
                    %   CID = CID';
                    %   O2  similary measurement method2
                    CID = find(associate==associate(ff));
                    net_agg = model_agg(netsum,CID');
                    net_opt = net_agg;
                    flag_agg = 1;
                else
                    net_opt = netsum{ff};
                    flag_agg = 0;
                end
                %                 net_opt = netsum{ff};
            else
                net_opt =  netsum{ff};
                flag_agg = 0;

            end

            bu = bu_{ff};
            bd = bd_{ff};
            if 0==0
                if 1 == 1
                    if length(PopNewDec) < client_num
                        Decs_temp = [];
                    else
                        Decs_temp = [PopNewDec{ff}];
                    end
                else
                    if length(PopNewDec) < client_num
                        Decs_temp = [A1Dec_INI_SUM{ff}];
                    else
                        Decs_temp = [A1Dec_INI_SUM{ff};PopNewDec{ff}];
                    end
                end
            else
                if length(PopNewDec) < client_num
                    Decs_temp = [];
                else
                    Decs_temp = [];
                    for ff_ = 1:client_num
                        Decs_temp = [Decs_temp;(PopNewDec{ff_}-bd_{ff_})./(bu_{ff_}-bd_{ff_})];
                    end
                    Decs_temp = Decs_temp*(bu-bd)+bd;
                end
            end
            name = name_{ff};
            M = M_{ff};
            opt = opt_{ff};


            if ~isempty(Decs_temp)
                Decs = rand(popsize-size(Decs_temp,1),D)*(bu-bd)+bd;
                Decs = [Decs;Decs_temp];
                popsize = size(Decs,1);
                if mod(popsize,2)==1
                    Decs = [rand(1,D)*(bu-bd)+bd;Decs];
                    popsize = size(Decs,1);
                end
            else
                Decs = rand(popsize,D)*(bu-bd)+bd;
            end

            shuffleid = randperm(size(Decs,1),popsize);
            Decs = Decs(shuffleid,:);




            for j = 1:wmax
                rank    = randperm(popsize);
                loser   = rank(1:end/2);
                winner  = rank(end/2+1:end);


                id = 1;
                XXs_opt =[];
                for i = 1:size(loser,2)
                    XXs_opt(id,:) = [Decs(loser(i),:) Decs(winner(i),:)];
                    id = id + 1;
                end

                %                 XXs_tst_nor = mapminmax('apply',XXs_opt',TrainIn_struct)';

                XXs_tst_nor = (XXs_opt - bd) / (bu - bd);

                XXs_opt_Pre = onehotconv(net_opt(XXs_tst_nor')',2);

                replace = find(XXs_opt_Pre==-1);


                temp            = loser(replace);
                loser(replace)  = winner(replace);
                winner(replace) = temp;
                % Update the losers by learning from the winners
                LoserDec  = Decs(loser,:);
                WinnerDec = Decs(winner,:);


                if j==1
                    LoserVel  = zeros(size(LoserDec));
                    WinnerVel  = zeros(size(WinnerDec));
                end


                R1  = repmat(rand(popsize/2,1),1,D);
                R2  = repmat(rand(popsize/2,1),1,D);
                R3  = repmat(rand(popsize/2,1),1,D);

                                LoserVel = R1.*LoserVel + phi*R2.*(WinnerDec-LoserDec) + R3*(1-0).*(repmat(mean([Decs],1),popsize/2,1)-LoserDec);
%                 LoserVel = R1.*LoserVel + R2.*(WinnerDec-LoserDec) + R3*phi.*(repmat(mean([Decs],1),popsize/2,1)-LoserDec);

                LoserDec = LoserDec + LoserVel;

                LoserDec(LoserDec< bd*ones(size(LoserDec,1),size(LoserDec,2)))=bd;
                LoserDec(LoserDec> bu*ones(size(LoserDec,1),size(LoserDec,2)))=bu;
                Decs = [WinnerDec;LoserDec];

            end


            PopNew = Decs(randperm(size(Decs,1),1),:);
            if ~isempty(M)
                new_Y1 = feval(name,PopNew,M,opt);
            else
                new_Y1 = feval(name,PopNew);
            end

            if length(PopNewDec)~=client_num
                PopNewDec{ff} = PopNew;
                PopNewObj{ff} = new_Y1(1,:);
            else
                PopNewDec{ff} = [PopNewDec{ff};PopNew];
                PopNewObj{ff} = [PopNewObj{ff};new_Y1(1,:)];
            end


        end
        maxround = maxround + 1;

        %         if sum(Decs_temp_EXIST == A1Dec(gbest_real,:)) == D
        %             disp('guess right')
        %         end

%         disp('----------------------')
        disp(min([A1Obj_INI_SUM{1};PopNewObj{1}]))
%         disp(min([A1Obj_INI_SUM{2};PopNewObj{2}]))
%         disp(min([A1Obj_INI_SUM{3};PopNewObj{3}]))
%         disp(min([A1Obj_INI_SUM{4};PopNewObj{4}]))
%         disp(min([A1Obj_INI_SUM{5};PopNewObj{5}]))
    end

    %     Data = [[A1Dec_INI_SUM1;PopNewDec1] [A1Obj_INI_SUM1;PopNewObj1] [A1Dec_INI_SUM2;PopNewDec2] [A1Obj_INI_SUM2;PopNewObj2]];

    Data = [];
    for ff = 1:client_num
        Data = [Data [A1Dec_INI_SUM{ff};PopNewDec{ff}]];
    end

    %data{ll} = Data;
    filename= ['MCSO_W3C2O2_',num2str(client_num),'tasks_p',num2str(p),'_AF',num2str(UCB_Flag),'_D',num2str(D),'_MAXFE',num2str(MAXFE+N),'_trs',num2str(flag_transfer),'_clu',num2str(cl_num),'_r*.mat'];
    [picsum,~] = size(dir(filename));
    picsum = picsum + 1;
    filename= ['MCSO_W3C2O2_',num2str(client_num),'tasks_p',num2str(p),'_AF',num2str(UCB_Flag),'_D',num2str(D),'_MAXFE',num2str(MAXFE+N),'_trs',num2str(flag_transfer),'_clu',num2str(cl_num),'_r',num2str(picsum),'.mat'];

    save(filename,'Data')
    ll = ll + 1 ;




end





function net_agg = model_agg(netsum,CID)
net_ = netsum{1};
net = netsum{1};
client_num = size(CID,2);
IW_agg  = zeros(size(netsum{1}.IW{1,1},1),size(netsum{1}.IW{1,1},2));
for i = CID
    IW_agg  = IW_agg + netsum{i}.IW{1,1};
end

IW_agg = IW_agg/client_num;
net_.IW{1,1} = IW_agg;

LW_agg = {};

for i = size(net.LW,1)
    for j = size(net.LW,2)
        if ~isempty(net.LW{i,j})
            LW_agg  = zeros(size(netsum{1}.LW{i,j},1),size(netsum{1}.LW{i,j},2));
            for t = CID
                LW_agg  = LW_agg + netsum{t}.LW{i,j};
            end
            LW_agg = LW_agg/client_num;
            net_.LW{i,j} = LW_agg;
        else
            net_.LW{i,j} = [];
        end
    end
end

for i = size(net.b,1)
    if ~isempty(net.b{i,1})
        b_agg  = zeros(size(netsum{1}.b{i,1},1),size(netsum{1}.b{i,1},2));
        for t = CID
            b_agg  = b_agg + netsum{t}.b{i,1};
        end
        b_agg = b_agg/client_num;
        net_.b{i,1} = b_agg;
    else
        net_.b{i,1} = [];
    end
end



net_agg = net_;
end


function weightvector = ConvertModelParaToVector(netsum,CID)

net = netsum{CID};
weightvector = [];
% IW_agg  = zeros(size(netsum{1}.IW{1,1},1),size(netsum{1}.IW{1,1},2));
for i = CID
    aa=  netsum{i}.IW{1,1};
end

IWVector = reshape(aa,1,size(aa,1)*size(aa,2));
weightvector = [weightvector IWVector];

% IW_agg = IW_agg/client_num;
% net_.IW{1,1} = IW_agg;

LW_agg = {};

for i = size(net.LW,1)
    for j = size(net.LW,2)
        if ~isempty(net.LW{i,j})
            %             LW_agg  = zeros(size(netsum{1}.LW{i,j},1),size(netsum{1}.LW{i,j},2));
            for t = CID
                aa = [];
                aa =  netsum{t}.LW{i,j};
                LWVector = reshape(aa,1,size(aa,1)*size(aa,2));
                weightvector = [weightvector LWVector];
            end
            %             LW_agg = LW_agg/client_num;
            %             net_.LW{i,j} = LW_agg;
        else
            %             net_.LW{i,j} = [];
        end
    end
end

for i = size(net.b,1)
    if ~isempty(net.b{i,1})
        %         b_agg  = zeros(size(netsum{1}.b{i,1},1),size(netsum{1}.b{i,1},2));
        for t = CID

            aa = [];
            aa =  netsum{t}.b{i,1};
            BVector = reshape(aa,1,size(aa,1)*size(aa,2));
            weightvector = [weightvector BVector];
        end
        %         b_agg = b_agg/client_num;
        %         net_.b{i,1} = b_agg;
    else
        %         net_.b{i,1} = [];
    end
end



end







function objs = AF(PopObj,MSE,A1Obj,UCB_Flag)


if UCB_Flag==1
    %         UCB
    objs = PopObj + 2*sqrt(MSE);
elseif UCB_Flag==0
    %         EI
    YY = PopObj;
    s = MSE;
    fmin = min(A1Obj);
    set=fmin-YY;
    s=s.^0.5;
    if s==0
        if YY<fmin
            E=fmin-YY;
        else
            E=0;
        end
    else
        E=set.*normcdf(set./s)+s.*normpdf(set./s);
    end
    objs=-E;
elseif UCB_Flag==2
    objs = PopObj - 2*sqrt(MSE);
else
    UCB_Flag
end


end


function [name,bu,bd,vmax,vmin,M,opt] = ChooseProblem(ifu,D)

flag_multitask = 1;
M = [];
opt = [];

if flag_multitask == 1

    % CI_HS
    % Griewank
    if ifu == 1
        temp = load('50d/CI_H.mat');
        M = temp.Rotation_Task1(1:D,1:D);
        opt = temp.GO_Task1(1:D);

        name=@Griewank_;
        global bu bd
        bu=100;
        bd=-100;
        vmax=100;
        vmin=-100;
    end

    % Rastrigin
    if ifu == 2
        temp = load('50d/CI_H.mat');
        M = temp.Rotation_Task2(1:D,1:D);
        opt = temp.GO_Task2(1:D);

        name=@Rastrigin_;
        global bu bd
        bu=50;
        bd=-50;
        vmax=50;
        vmin=-50;
    end



    % CI_MS_t1
    % Ackley
    if ifu == 3
        temp = load('50d/CI_M.mat');
        M = temp.Rotation_Task1(1:D,1:D);
        opt = temp.GO_Task1(1:D);

        name=@Ackley_;
        global bu bd
        bu=50;
        bd=-50;
        vmax=50;
        vmin=-50;
    end

    % Rastrigin
    if ifu == 4
        temp = load('50d/CI_M.mat');
        M = temp.Rotation_Task2(1:D,1:D);
        opt = temp.GO_Task2(1:D);

        name=@Rastrigin_;
        global bu bd
        bu=50;
        bd=-50;
        vmax=50;
        vmin=-50;
    end

    % CI_LS_t1
    % Ackley
    if ifu == 5
        temp = load('50d/CI_L.mat');
        M = temp.Rotation_Task1(1:D,1:D);
        opt = temp.GO_Task1(1:D);

        name=@Ackley_;
        global bu bd
        bu=50;
        bd=-50;
        vmax=50;
        vmin=-50;
    end

    % Schwefel
    if ifu == 6
        M = eye(D);
        opt = zeros(1,D);

        name=@Schwefel_;
        global bu bd
        bu=500;
        bd=-500;
        vmax=500;
        vmin=-500;
    end


    % PI_HS
    % Rastrigin
    if ifu == 7
        temp = load('50d/PI_H.mat');
        M = temp.Rotation_Task1(1:D,1:D);
        opt = temp.GO_Task1(1:D);

        name=@Rastrigin_;
        global bu bd
        bu=50;
        bd=-50;
        vmax=50;
        vmin=-50;
    end

    % Sphere
    if ifu == 8
        temp = load('50d/PI_H.mat');
        M = eye(D);
        opt = temp.GO_Task2(1:D);


        name=@Sphere_;
        global bu bd
        bu=100;
        bd=-100;
        vmax=100;
        vmin=-100;
    end

    % PI_MS
    if ifu == 9
        temp = load('50d/PI_M.mat');
        M = temp.Rotation_Task1(1:D,1:D);
        opt = temp.GO_Task1(1:D);

        name=@Ackley_;
        global bu bd
        bu=50;
        bd=-50;
        vmax=50;
        vmin=-50;
    end


    if ifu == 10
        M = eye(D);
        opt = zeros(1,D);

        name=@Rosenbrock_;
        global bu bd
        bu=50;
        bd=-50;
        vmax=50;
        vmin=-50;
    end

    % PI_LS
    if ifu == 11
        temp = load('50d/PI_L.mat');
        M = temp.Rotation_Task1(1:D,1:D);
        opt = temp.GO_Task1(1:D);

        name=@Ackley_;
        global bu bd
        bu=50;
        bd=-50;
        vmax=50;
        vmin=-50;
    end

    if ifu == 12
%         temp = load('50d/PI_L.mat');
%         M = temp.Rotation_Task2(1:D,1:D);
%         opt = temp.GO_Task2(1:D);

        M = eye(D);
        opt = zeros(1,D);

        name=@Weierstrass_;
        global bu bd
        bu=0.5;
        bd=-0.5;
        vmax=0.5;
        vmin=-0.5;
    end

    % NI_HS
    % Rosenbrock
    if ifu == 13
        M = eye(D);
        opt = zeros(1,D);

        name=@Rosenbrock_;
        global bu bd
        bu=50;
        bd=-50;
        vmax=50;
        vmin=-50;
    end

    % Rastrigin
    if ifu == 14
        temp = load('50d/NI_H.mat');
        M = temp.Rotation_Task2(1:D,1:D);
        opt = temp.GO_Task2(1:D);

        name=@Rastrigin_;
        global bu bd
        bu=50;
        bd=-50;
        vmax=50;
        vmin=-50;
    end

    % NI_MS
    % Griewank
    if ifu == 15
        temp = load('50d/NI_M.mat');
        M = temp.Rotation_Task1(1:D,1:D);
        opt = temp.GO_Task1(1:D);


        name=@Griewank_;
        global bu bd
        bu=100;
        bd=-100;
        vmax=100;
        vmin=-100;
    end

    % Weierstrass
    if ifu == 16
        temp = load('50d/NI_M.mat');
        M = temp.Rotation_Task2(1:D,1:D);
        opt = temp.GO_Task2(1:D);

        name=@Weierstrass_;
        global bu bd
        bu= 0.5;
        bd=- 0.5;
        vmax= 0.5;
        vmin=- 0.5;
    end


    % NI_LS
    if ifu == 17
        temp = load('50d/NI_L.mat');
        M = temp.Rotation_Task1(1:D,1:D);
        opt = temp.GO_Task1(1:D);

        name=@Rastrigin_;
        global bu bd
        bu=50;
        bd=-50;
        vmax=50;
        vmin=-50;
    end

    if ifu == 18
        M = eye(D);
        opt = zeros(1,D);

        name=@Schwefel_;
        global bu bd
        bu=500;
        bd=-500;
        vmax=500;
        vmin=-500;
    end

elseif flag_multitask == 2

    % CI_HS
    if ifu == 1
        name=@CI_HS_t1;
        global bu bd
        bu=600;
        bd=-600;
        vmax=600;
        vmin=-600;
    end

    if ifu == 2
        name=@CI_HS_t2;
        global bu bd
        bu=5.12;
        bd=-5.12;
        vmax=5.12;
        vmin=-5.12;
    end

    % CI_LS_t1
    if ifu == 3
        name=@CI_LS_t1;
        global bu bd
        bu=32;
        bd=-32;
        vmax=32;
        vmin=-32;
    end

    if ifu == 4
        name=@CI_LS_t2;
        global bu bd
        bu=500;
        bd=-500;
        vmax=500;
        vmin=-500;
    end

    % CI_MS_t1

    if ifu == 5
        name=@CI_MS_t1;
        global bu bd
        bu=32;
        bd=-32;
        vmax=32;
        vmin=-32;
    end

    if ifu == 6
        name=@CI_MS_t2;
        global bu bd
        bu=5.12;
        bd=-5.12;
        vmax=5.12;
        vmin=-5.12;
    end


    % NI_HS

    if ifu == 7
        name=@NI_HS_t1;
        global bu bd
        bu=32;
        bd=-32;
        vmax=32;
        vmin=-32;
    end

    if ifu == 8
        name=@NI_HS_t2;
        global bu bd
        bu=5.12;
        bd=-5.12;
        vmax=5.12;
        vmin=-5.12;
    end

    % NI_MS
    if ifu == 9
        name=@NI_MS_t1;
        global bu bd
        bu=600;
        bd=-600;
        vmax=600;
        vmin=-600;
    end

    if ifu == 10
        name=@NI_MS_t2;
        global bu bd
        bu= 0.5;
        bd=- 0.5;
        vmax= 0.5;
        vmin=- 0.5;
    end

else

    if ifu==1
        %%%%%%%%%%%%%%%%%%%%%%%%%%%%%_____1.单峰函数Ellipsoid
        name=@Ellipsoid;
        global bu bd
        bu=5.12;
        bd=-5.12;
        vmax=5.12;
        vmin=-5.12;
    end
    if ifu==2
        %%%%%%%%%%%%%%%%%%%%%%%%%%%%%_____2.多峰函数Rosenbrock
        name=@Rosenbrock;
        global bu bd
        bu=2.048;
        bd=-2.048;
        vmax=2.048;
        vmin=-2.048;
    end
    if ifu==3
        %%%%%%%%%%%%%%%%%%%%%%%%%%%%%_____3.多峰函数Griewank
        name=@Griewank;
        global bu bd
        bu=600;
        bd=-600;
        vmax=600;
        vmin=-600;
    end
    if ifu==4
        %%%%%%%%%%%%%%%%%%%%%%%%%%%%%_____4.多峰函数Ackley
        name=@Ackley;
        global  bu bd
        bu=32.768;
        bd=-32.768;
        vmax=32.768;
        vmin=-32.768;
    end

    if ifu==5
        %%%%%%%%%%%%%%%%%%%%%%%%%%%%%_____6.CEC_5005_func_num=10,fmin=-330,多峰函数shifted Rotated Rastring
        name=@benchmark_func;
        global func_num
        func_num=10;
        global bu bd
        bu=5;
        bd=-5;
        vmax=5;
        vmin=-5;
    end
    if ifu==6
        %%%%%%%%%%%%%%%%%%%%%%%%%%%%%_____7.CEC_2005_func_num=16,fmin=120,多峰函数Rotated Hybrid Composition Function
        name=@benchmark_func;
        global func_num
        func_num=16;
        global bu bd
        bu=5;
        bd=-5;
        vmax=5;
        vmin=-5;
    end
    if ifu==7
        %%%%%%%%%%%%%%%%%%%%%%%%%%%%%_____8.CEC_2005_func_num=19,fmin=10,多峰函数Rotated Hybrid Composition Function with a Narrow Basin for the Global Optimum
        name=@benchmark_func;
        global func_num
        func_num=19;
        global bu bd
        bu=5;
        bd=-5;
        vmax=5;
        vmin=-5;
    end
    if ifu==8
        %%%%%%%%%%%%%%%%%%%%%%%%%%%%%_____8.CEC_2005_func_num=6,fmin=390,多峰函数Shifted Rosenbrock's  Function
        name=@benchmark_func;
        global func_num
        func_num=6;
        global bu bd
        bu=100;
        bd=-100;
        vmax=100;
        vmin=-100;
    end
    if ifu==9
        %%%%%%%%%%%%%%%%%%%%%%%%%%%%%_____8.CEC_2005_func_num=9,fmin=-330,多峰函数Shifted Rastrigin's  Function
        name=@benchmark_func;
        global func_num
        func_num=9;
        global bu bd
        bu=5;
        bd=-5;
        vmax=5;
        vmin=-5;
    end
    if ifu==10
        %%%%%%%%%%%%%%%%%%%%%%%%%%%%%_____8.CEC_2005_func_num=3,fmin=-450,单峰函数Shifted Rotated High Conditioned Elliptic Function
        name=@benchmark_func;
        global func_num
        func_num=3;
        global bu bd
        bu=100;
        bd=-100;
        vmax=100;
        vmin=-100;
    end
    if ifu==11
        %%%%%%%%%%%%%%%%%%%%%%%%%%%%%_____8.CEC_2005_func_num=9,fmin=-330,多峰函数Shifted Rastrigin's  Function
        name=@benchmark_func;
        global func_num
        func_num=4;
        global bu bd
        bu=100;
        bd=-100;
        vmax=100;
        vmin=-100;
    end

    if ifu==12
        %%%%%%%%%%%%%%%%%%%%%%%%%%%%%_____8.CEC_2005_func_num=9,fmin=-330,多峰函数Shifted Rastrigin's  Function
        name=@benchmark_func;
        global func_num
        func_num=5;
        global bu bd
        bu=100;
        bd=-100;
        vmax=100;
        vmin=-100;
    end




    if ifu==13
        %%%%%%%%%%%%%%%%%%%%%%%%%%%%%_____8.CEC_2005_func_num=9,fmin=-330,多峰函数Shifted Rastrigin's  Function
        name=@benchmark_func;
        global func_num
        func_num=7;
        global bu bd
        bu=600;
        bd=-600;
        vmax=600;
        vmin=-600;
    end



    if ifu==14
        %%%%%%%%%%%%%%%%%%%%%%%%%%%%%_____8.CEC_2005_func_num=9,fmin=-330,多峰函数Shifted Rastrigin's  Function
        name=@benchmark_func;
        global func_num
        func_num=11;
        global bu bd
        bu=0.5;
        bd=-0.5;
        vmax=0.5;
        vmin=-0.5;
    end



    if ifu==15
        %%%%%%%%%%%%%%%%%%%%%%%%%%%%%_____8.CEC_2005_func_num=9,fmin=-330,多峰函数Shifted Rastrigin's  Function
        name=@benchmark_func;
        global func_num
        func_num=12;
        global bu bd
        bu=pi;
        bd=-pi;
        vmax=pi;
        vmin=-pi;
    end



    if ifu==16
        %%%%%%%%%%%%%%%%%%%%%%%%%%%%%_____8.CEC_2005_func_num=9,fmin=-330,多峰函数Shifted Rastrigin's  Function
        name=@benchmark_func;
        global func_num
        func_num=13;
        global bu bd
        bu=1;
        bd=-3;
        vmax=1;
        vmin=-3;
    end



    if ifu==17
        %%%%%%%%%%%%%%%%%%%%%%%%%%%%%_____8.CEC_2005_func_num=9,fmin=-330,多峰函数Shifted Rastrigin's  Function
        name=@benchmark_func;
        global func_num
        func_num=14;
        global bu bd
        bu=100;
        bd=-100;
        vmax=100;
        vmin=-100;
    end



    if ifu==18
        %%%%%%%%%%%%%%%%%%%%%%%%%%%%%_____8.CEC_2005_func_num=9,fmin=-330,多峰函数Shifted Rastrigin's  Function
        name=@benchmark_func;
        global func_num
        func_num=15;
        global bu bd
        bu=5;
        bd=-5;
        vmax=5;
        vmin=-5;
    end



    if ifu==19
        %%%%%%%%%%%%%%%%%%%%%%%%%%%%%_____8.CEC_2005_func_num=9,fmin=-330,多峰函数Shifted Rastrigin's  Function
        name=@benchmark_func;
        global func_num
        func_num=17;
        global bu bd
        bu=5;
        bd=-5;
        vmax=5;
        vmin=-5;
    end



    if ifu==20
        %%%%%%%%%%%%%%%%%%%%%%%%%%%%%_____8.CEC_2005_func_num=9,fmin=-330,多峰函数Shifted Rastrigin's  Function
        name=@benchmark_func;
        global func_num
        func_num=18;
        global bu bd
        bu=5;
        bd=-5;
        vmax=5;
        vmin=-5;
    end

    if ifu==21
        %%%%%%%%%%%%%%%%%%%%%%%%%%%%%_____8.CEC_2005_func_num=9,fmin=-330,多峰函数Shifted Rastrigin's  Function
        name=@benchmark_func;
        global func_num
        func_num=20;
        global bu bd
        bu=5;
        bd=-5;
        vmax=5;
        vmin=-5;
    end

    if ifu==22
        %%%%%%%%%%%%%%%%%%%%%%%%%%%%%_____8.CEC_2005_func_num=9,fmin=-330,多峰函数Shifted Rastrigin's  Function
        name=@benchmark_func;
        global func_num
        func_num=21;
        global bu bd
        bu=5;
        bd=-5;
        vmax=5;
        vmin=-5;
    end

    if ifu==23
        %%%%%%%%%%%%%%%%%%%%%%%%%%%%%_____8.CEC_2005_func_num=9,fmin=-330,多峰函数Shifted Rastrigin's  Function
        name=@benchmark_func;
        global func_num
        func_num=22;
        global bu bd
        bu=5;
        bd=-5;
        vmax=5;
        vmin=-5;
    end


    if ifu==24
        %%%%%%%%%%%%%%%%%%%%%%%%%%%%%_____8.CEC_2005_func_num=9,fmin=-330,多峰函数Shifted Rastrigin's  Function
        name=@benchmark_func;
        global func_num
        func_num=23;
        global bu bd
        bu=5;
        bd=-5;
        vmax=5;
        vmin=-5;
    end


    if ifu==25
        %%%%%%%%%%%%%%%%%%%%%%%%%%%%%_____8.CEC_2005_func_num=9,fmin=-330,多峰函数Shifted Rastrigin's  Function
        name=@benchmark_func;
        global func_num
        func_num=24;
        global bu bd
        bu=5;
        bd=-5;
        vmax=5;
        vmin=-5;
    end


end

end

